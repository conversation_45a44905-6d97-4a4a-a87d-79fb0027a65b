import 'package:flutter/material.dart';

/// Enhanced step progress bar with modern design and better visual hierarchy
class StepProgressBar extends StatelessWidget {
  final int currentStep;
  final List<String> steps;

  const StepProgressBar({
    super.key,
    required this.currentStep,
    required this.steps,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.all(20),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.08),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            children: List.generate(steps.length, (index) {
              final isCompleted = index < currentStep;
              final isActive = index == currentStep;
              final isLast = index == steps.length - 1;

              return Expanded(
                child: Row(
                  children: [
                    Expanded(
                      child: Column(
                        children: [
                          // Step indicator
                          Container(
                            width: 40,
                            height: 40,
                            decoration: BoxDecoration(
                              color:
                                  isCompleted || isActive
                                      ? const Color(0xFF12306E)
                                      : Colors.grey[300],
                              shape: BoxShape.circle,
                              boxShadow:
                                  isActive
                                      ? [
                                        BoxShadow(
                                          color: const Color(
                                            0xFF12306E,
                                          ).withValues(alpha: 0.3),
                                          blurRadius: 8,
                                          offset: const Offset(0, 2),
                                        ),
                                      ]
                                      : null,
                            ),
                            child:
                                isCompleted
                                    ? const Icon(
                                      Icons.check,
                                      color: Colors.white,
                                      size: 20,
                                    )
                                    : Center(
                                      child: Text(
                                        '${index + 1}',
                                        style: TextStyle(
                                          color:
                                              isActive
                                                  ? Colors.white
                                                  : Colors.grey[600],
                                          fontWeight: FontWeight.bold,
                                          fontSize: 16,
                                        ),
                                      ),
                                    ),
                          ),
                          const SizedBox(height: 8),
                          // Step title
                          Text(
                            steps[index],
                            style: TextStyle(
                              color:
                                  isActive || isCompleted
                                      ? const Color(0xFF12306E)
                                      : Colors.grey[600],
                              fontWeight:
                                  isActive
                                      ? FontWeight.bold
                                      : FontWeight.normal,
                              fontSize: 11,
                            ),
                            textAlign: TextAlign.center,
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                      ),
                    ),
                    if (!isLast)
                      Container(
                        width: 30,
                        height: 2,
                        margin: const EdgeInsets.only(bottom: 40),
                        decoration: BoxDecoration(
                          color:
                              isCompleted
                                  ? const Color(0xFF12306E)
                                  : Colors.grey[300],
                          borderRadius: BorderRadius.circular(1),
                        ),
                      ),
                  ],
                ),
              );
            }),
          ),
        ],
      ),
    );
  }
}
