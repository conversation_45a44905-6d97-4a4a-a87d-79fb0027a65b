import 'dart:io';
import 'package:flutter/material.dart';
import 'package:lc_work_flow/widgets/customer/customer_section_title.dart';
import 'package:lc_work_flow/models/collateral_type.dart';
import 'package:lc_work_flow/widgets/customer/image_preview_screen.dart';
import 'package:image_picker/image_picker.dart';

class DocumentsStep extends StatelessWidget {
  final List<File> idCardImages;
  final ValueChanged<List<File>> onIdCardImagesChanged;
  final Map<CollateralType, List<File>> collateralImages;
  final Set<CollateralType> selectedCollateralTypes;
  final Function(CollateralType, List<File>) onImagePicked;

  const DocumentsStep({
    super.key,
    required this.idCardImages,
    required this.onIdCardImagesChanged,
    required this.collateralImages,
    required this.selectedCollateralTypes,
    required this.onImagePicked,
  });

  @override
  Widget build(BuildContext context) {
    // IMPORTANT: Do NOT wrap this widget in a SingleChildScrollView or another vertical scrollable.
    // The PageView must have a fixed height to be swipeable.
    return Card(
      elevation: 2,
      margin: const EdgeInsets.symmetric(horizontal: 4.0),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            CustomerSectionTitle(
              title: 'Documents',
              icon: Icons.insert_drive_file,
            ),
            const SizedBox(height: 16),
            // ID Card block
            _buildImageCarousel(
              context,
              title: 'ID Card',
              images: idCardImages,
              onAddImage: (newList) async {
                onIdCardImagesChanged(newList);
              },
              onRemove: (index) {
                final newList = List<File>.from(idCardImages)..removeAt(index);
                onIdCardImagesChanged(newList);
              },
            ),
            const SizedBox(height: 24),
            // Collateral types blocks
            ...selectedCollateralTypes.map((type) {
              final images = collateralImages[type] ?? [];
              return Padding(
                padding: const EdgeInsets.only(bottom: 24.0),
                child: _buildImageCarousel(
                  context,
                  title: type.displayName,
                  images: images,
                  onAddImage: (newList) async {
                    onImagePicked(type, newList);
                  },
                  onRemove: (index) {
                    final newList = List<File>.from(images)..removeAt(index);
                    onImagePicked(type, newList);
                  },
                ),
              );
            }),
          ],
        ),
      ),
    );
  }

  Widget _buildImageCarousel(
    BuildContext context, {
    required String title,
    required List<File> images,
    required Future<void> Function(List<File> newImages) onAddImage,
    required Function(int) onRemove,
  }) {
    return _ImageCarousel(
      title: title,
      images: images,
      onAddImage: onAddImage,
      onRemove: onRemove,
    );
  }
}

class _ImageCarousel extends StatefulWidget {
  final String title;
  final List<File> images;
  final Future<void> Function(List<File> newImages) onAddImage;
  final Function(int) onRemove;

  const _ImageCarousel({
    required this.title,
    required this.images,
    required this.onAddImage,
    required this.onRemove,
  });

  @override
  State<_ImageCarousel> createState() => _ImageCarouselState();
}

class _ImageCarouselState extends State<_ImageCarousel> {
  final PageController _pageController = PageController(viewportFraction: 0.8);
  int _currentPage = 0;
  int _lastImageCount = 0;

  void _scrollToEnd() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_pageController.hasClients) {
        _pageController.animateToPage(
          widget.images.length, // last page (the + button)
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  @override
  void didUpdateWidget(covariant _ImageCarousel oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.images.length > _lastImageCount) {
      _scrollToEnd();
    }
    _lastImageCount = widget.images.length;
  }

  @override
  void initState() {
    super.initState();
    _lastImageCount = widget.images.length;
  }

  void _handleRemove(int index) {
    widget.onRemove(index);
    // After removal, scroll to end if there are still images
    if (widget.images.length > 1) {
      Future.delayed(const Duration(milliseconds: 250), _scrollToEnd);
    }
  }

  @override
  Widget build(BuildContext context) {
    final totalPages = widget.images.length + 1;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          widget.title,
          style: Theme.of(
            context,
          ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 8),
        SizedBox(
          height: 200,
          child: PageView.builder(
            controller: _pageController,
            itemCount: totalPages,
            onPageChanged: (index) {
              setState(() {
                _currentPage = index;
              });
            },
            itemBuilder: (context, index) {
              if (index == widget.images.length) {
                // Add button card
                return Center(
                  child: Tooltip(
                    message: 'Add Photo',
                    child: GestureDetector(
                      onTap: () async {
                        final picker = ImagePicker();
                        final XFile? image = await picker.pickImage(
                          source: ImageSource.camera,
                          imageQuality: 90,
                        );
                        if (image != null) {
                          final newList = List<File>.from(widget.images)
                            ..add(File(image.path));
                          await widget.onAddImage(newList);
                        }
                      },
                      child: Card(
                        elevation: 2,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(16),
                        ),
                        child: Container(
                          width: 180,
                          height: 180,
                          decoration: BoxDecoration(
                            color: Colors.blue[50],
                            border: Border.all(color: Colors.blue),
                            borderRadius: BorderRadius.circular(16),
                          ),
                          child: const Center(
                            child: Icon(
                              Icons.add_a_photo,
                              size: 32,
                              color: Colors.blue,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                );
              } else {
                final file = widget.images[index];
                return Center(
                  child: AnimatedSwitcher(
                    duration: const Duration(milliseconds: 250),
                    child: Stack(
                      key: ValueKey(file.path),
                      children: [
                        GestureDetector(
                          onTap: () {
                            Navigator.of(context).push(
                              MaterialPageRoute(
                                builder:
                                    (_) => ImagePreviewScreen(
                                      imagePath: file.path,
                                      label: widget.title,
                                    ),
                              ),
                            );
                          },
                          child: Card(
                            elevation: 2,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(16),
                            ),
                            child: ClipRRect(
                              borderRadius: BorderRadius.circular(16),
                              child: Image.file(
                                file,
                                fit: BoxFit.cover,
                                width: 180,
                                height: 180,
                              ),
                            ),
                          ),
                        ),
                        Positioned(
                          top: 8,
                          right: 8,
                          child: GestureDetector(
                            onTap: () => _handleRemove(index),
                            child: Container(
                              decoration: BoxDecoration(
                                color: Colors.black54,
                                shape: BoxShape.circle,
                              ),
                              child: const Icon(
                                Icons.close,
                                color: Colors.white,
                                size: 20,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              }
            },
          ),
        ),
        const SizedBox(height: 8),
        SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: List.generate(widget.images.length, (index) {
              return GestureDetector(
                onTap: () {
                  _pageController.animateToPage(
                    index,
                    duration: const Duration(milliseconds: 300),
                    curve: Curves.easeInOut,
                  );
                },
                child: AnimatedContainer(
                  duration: const Duration(milliseconds: 200),
                  width: _currentPage == index ? 24.0 : 12.0,
                  height: 12.0,
                  margin: const EdgeInsets.symmetric(horizontal: 4.0),
                  decoration: BoxDecoration(
                    shape: BoxShape.rectangle,
                    borderRadius: BorderRadius.circular(6.0),
                    color: _currentPage == index 
                        ? Theme.of(context).primaryColor 
                        : Colors.grey[300],
                  ),
                ),
              );
            }),
          ),
        ),
      ],
    );
  }
}
