import 'package:flutter/material.dart';
import 'package:lc_work_flow/widgets/customer/customer_section_title.dart';
import 'package:lc_work_flow/screen/dashboard/screens/add_customer_loan_info_section.dart';
import 'package:lc_work_flow/screen/dashboard/screens/add_customer_collateral_section.dart';
import 'package:lc_work_flow/screen/dashboard/screens/add_customer_guarantor_section.dart';
import 'package:lc_work_flow/widgets/customer/customer_date_picker_field.dart';
import 'package:lc_work_flow/models/collateral_type.dart';

class LoanInfoStep extends StatelessWidget {
  final TextEditingController requestedAmountController;
  final dynamic selectedProductType;
  final Function(dynamic) onProductTypeChanged;
  final TextEditingController loanTermController;
  final DateTime? disbursementDate;
  final Function(DateTime?) onDisbursementDateChanged;
  final Set<CollateralType> selectedCollateralTypes;
  final Function(CollateralType, bool) onCollateralTypeChanged;
  final TextEditingController guarantorNameController;
  final TextEditingController guarantorPhoneController;
  final FocusNode guarantorNameFocusNode;
  final FocusNode guarantorPhoneFocusNode;
  final bool showGuarantorSection;

  const LoanInfoStep({
    super.key,
    required this.requestedAmountController,
    required this.selectedProductType,
    required this.onProductTypeChanged,
    required this.loanTermController,
    required this.disbursementDate,
    required this.onDisbursementDateChanged,
    required this.selectedCollateralTypes,
    required this.onCollateralTypeChanged,
    required this.guarantorNameController,
    required this.guarantorPhoneController,
    required this.guarantorNameFocusNode,
    required this.guarantorPhoneFocusNode,
    required this.showGuarantorSection,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      margin: const EdgeInsets.symmetric(horizontal: 4.0),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            CustomerSectionTitle(
              title: 'Loan Information',
              icon: Icons.account_balance,
            ),
            const SizedBox(height: 16),
            CustomerLoanInfoSection(
              requestedAmountController: requestedAmountController,
              selectedProductType: selectedProductType,
              onProductTypeChanged: onProductTypeChanged,
              loanTermController: loanTermController,
            ),
            const SizedBox(height: 16),
            CustomerCollateralSection(
              selectedCollateralTypes: selectedCollateralTypes,
              onCollateralTypeChanged: onCollateralTypeChanged,
            ),
            if (showGuarantorSection)
              Padding(
                padding: const EdgeInsets.only(top: 16.0),
                child: CustomerGuarantorSection(
                  guarantorNameController: guarantorNameController,
                  guarantorPhoneController: guarantorPhoneController,
                  guarantorNameFocusNode: guarantorNameFocusNode,
                  guarantorPhoneFocusNode: guarantorPhoneFocusNode,
                  show: showGuarantorSection,
                ),
              ),
            const SizedBox(height: 16),
            CustomerDatePickerField(
              label: 'Disbursement Date',
              selectedDate: disbursementDate,
              onDateSelected: onDisbursementDateChanged,
              validator:
                  (date) =>
                      date == null
                          ? 'Please select the disbursement date'
                          : null,
            ),
          ],
        ),
      ),
    );
  }
}
